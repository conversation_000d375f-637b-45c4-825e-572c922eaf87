---
type: "always_apply"
---

Update to improve performance, accessibility, maintainable, error handling, add memoization where neccesary for heavy computation, add meaningful comments, respect reduce motion when animating import useReducedMotion from "framer-motion", and for best practices.
Make sure no to break any functionalities and layout setup. Extract any values possible to constants, just keep them in the same file if they are just a few lines. create a hook file if there is a chance to do so. Always follow Next.js best practices, linting errors, and type errors.
always double check for responsiveness for mobile device.
Whenever proposing a new or updated file use the markdown code block syntax and always add a file path in a comment on the top of the code block.
