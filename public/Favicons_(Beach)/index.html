<!doctype html>
<head>
    <title>Favicons</title>
    <meta charset="utf-8" />

    <!-- For old IEs -->
    <link rel="shortcut icon" href="favicon.ico" />

    <!-- For new browsers multisize ico  -->
    <link
        rel="icon"
        type="image/x-icon"
        sizes="16x16 32x32"
        href="favicon.ico"
    />

    <!-- Chrome for Android -->
    <link rel="icon" sizes="192x192" href="favicon-192.png" />

    <!-- For iPhone 6+ downscaled for other devices -->
    <link
        rel="apple-touch-icon"
        sizes="180x180"
        href="favicon-180-precomposed.png"
    />

    <!-- For IE10 Metro -->
    <meta name="msapplication-TileColor" content="#FFFFFF" />
    <meta
        name="msapplication-TileImage"
        content="favicon-114-precomposed.png"
    />

    <style>
        body {
            background-color: #f5f5f5;
            border: 0px;
            margin: 0px;
            padding: 0px;
            font-family:
                Consolas,
                Menlo,
                Monaco,
                Lucida Console,
                Liberation Mono,
                DejaVu Sans Mono,
                Bitstream Vera Sans Mono,
                Courier New,
                monospace,
                serif;
            color: black;
        }

        pre {
            margin: 0px;
            color: black;
            padding: 0px 5%;
        }

        code {
        }

        .container {
            background-color: white;
            max-width: 800px;
            width: 100%;
            margin: 0 auto;
            padding: 1% 0;
            height: 100%;
        }

        .comment {
            color: gray;
            padding: 0px;
            margin: 0px;
        }

        hr {
            width: 80%;
            padding: 0 5%;
            border-color: #f5f5f5;
            background-color: #d1d1d1;
        }

        p {
            padding: 1% 5%;
        }
    </style>
</head>
<body class="">
    <div class="container">
        <p>
            To use the favicons insert into your head section some of these tags
            accordly to your needs.
        </p>
        <hr />
        <pre>
            <code>
                <span class="comment">&lt;!-- For old IEs --&gt;</span>
                &lt;link rel=&quot;shortcut icon&quot; href=&quot;favicon.ico&quot; /&gt;
                
                <span class="comment">&lt;!-- For new browsers - multisize ico  --&gt;</span>
                &lt;link rel=&quot;icon&quot; type=&quot;image/x-icon&quot; sizes=&quot;16x16 32x32&quot; href=&quot;favicon.ico&quot;&gt;
                
                <span class="comment">&lt;!-- For iPad with high-resolution Retina display running iOS &ge; 7: --&gt;</span>
                &lt;link rel=&quot;apple-touch-icon&quot; sizes=&quot;152x152&quot; href=&quot;favicon-152-precomposed.png&quot;&gt;
                
                <span class="comment">&lt;!-- For iPad with high-resolution Retina display running iOS &le; 6: --&gt;</span>
                &lt;link rel=&quot;apple-touch-icon&quot; sizes=&quot;144x144&quot; href=&quot;favicon-144-precomposed.png&quot;&gt;
                
                <span class="comment">&lt;!-- For iPhone with high-resolution Retina display running iOS &ge; 7: --&gt;</span>
                &lt;link rel=&quot;apple-touch-icon&quot; sizes=&quot;120x120&quot; href=&quot;favicon-120-precomposed.png&quot;&gt;
                
                <span class="comment">&lt;!-- For iPhone with high-resolution Retina display running iOS &le; 6: --&gt;</span>
                &lt;link rel=&quot;apple-touch-icon&quot; sizes=&quot;114x114&quot; href=&quot;favicon-114-precomposed.png&quot;&gt;
                
                <span class="comment">&lt;!-- For iPhone 6+ --&gt;</span>
                &lt;link rel=&quot;apple-touch-icon&quot; sizes=&quot;180x180&quot; href=&quot;favicon-180-precomposed.png&quot;&gt;
                
                <span class="comment">&lt;!-- For first- and second-generation iPad: --&gt;</span>
                &lt;link rel=&quot;apple-touch-icon&quot; sizes=&quot;72x72&quot; href=&quot;favicon-72-precomposed.png&quot;&gt;
                
                <span class="comment">&lt;!-- For non-Retina iPhone, iPod Touch, and Android 2.1+ devices: --&gt;</span>
                &lt;link rel=&quot;apple-touch-icon&quot; sizes=&quot;57x57&quot; href=&quot;favicon-57.png&quot;&gt;
                
                <span class="comment">&lt;!-- For Old Chrome --&gt;</span>
                &lt;link rel=&quot;icon&quot; sizes=&quot;32x32&quot; href=&quot;favicon-32.png&quot; &gt;
                
                <span class="comment">&lt;!-- For IE10 Metro --&gt;</span>
                &lt;meta name=&quot;msapplication-TileColor&quot; content=&quot;#FFFFFF&quot;&gt;
                &lt;meta name=&quot;msapplication-TileImage&quot; content=&quot;favicon-144.png&quot;&gt;
                &lt;meta name=&quot;theme-color&quot; content=&quot;#ffffff&quot;&gt;
                
                <span class="comment">&lt;!-- Chrome for Android --&gt;</span>
                &lt;link rel=&quot;manifest&quot; href=&quot;manifest.json&quot;&gt;
                &lt;link rel=&quot;icon&quot; sizes=&quot;192x192&quot; href=&quot;favicon-192.png&quot;&gt;
                
            </code>
        </pre>

        <hr />

        <p>
            For more informations about favicons consult
            <a href="https://github.com/audreyr/favicon-cheat-sheet"
                >The Favicon Cheat Sheet</a
            >
            by Audrey Roy.
        </p>
    </div>
</body>
