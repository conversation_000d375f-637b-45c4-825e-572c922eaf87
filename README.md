# BaliBlissed - A Next.js & AI Powered Travel Agency App

Welcome to BaliBlissed, a comprehensive, AI-enhanced web application designed for a fictional Bali-based travel agency. This project showcases how to build a modern, feature-rich web application using a cutting-edge tech stack.

This application was built inside Firebase Studio, an AI-first development environment.

## Key Features

- **AI-Powered Itinerary Generation:** Users can input their interests, travel dates, and budget to receive a custom-tailored Bali itinerary generated by a Genkit AI flow.
- **AI Travel Assistant:** A floating chat widget allows users to ask questions about Bali or the travel packages. The AI is equipped with tools to fetch real-time package information to provide accurate answers.
- **Dynamic Contact Form:** The contact form provides an instant, AI-generated confirmation message to the user upon submission.
- **Private Car Charter Service:** A dedicated section and page highlight the agency's primary service.
- **Curated Packages & Destinations:** The application dynamically displays featured destinations and travel packages.
- **Modern, Responsive UI:** Built with shadcn/ui and Tailwind CSS, the application is fully responsive and includes a light/dark mode theme switcher.

## Tech Stack

- **Framework:** [Next.js](https://nextjs.org/) (using the App Router)
- **Language:** [TypeScript](https://www.typescriptlang.org/)
- **AI Integration:** [Genkit](https://firebase.google.com/docs/genkit) (with Google's Gemini models)
- **Styling:** [Tailwind CSS](https://tailwindcss.com/)
- **UI Components:** [shadcn/ui](https://ui.shadcn.com/)
- **Forms:** [React Hook Form](https://react-hook-form.com/) & [Zod](https://zod.dev/) for validation

## Getting Started

For complete instructions on how to set up, configure, and run this project locally for development, please see the [DEVELOPMENT.md](instructions/DEVELOPMENT.md) file.
