@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/* NEUMORPHISM THEME COLORS */
:root {
    --radius: 0.625rem;
    --background: #e0e5ec;
    --foreground: #2d3748;
    --card: #e0e5ec;
    --card-foreground: #2d3748;
    --popover: #e0e5ec;
    --popover-foreground: #2d3748;
    --primary: #4299e1;
    --primary-foreground: #ffffff;
    --secondary: #edf2f7;
    --secondary-foreground: #2d3748;
    --muted: #f7fafc;
    --muted-foreground: #718096;
    --accent: #edf2f7;
    --accent-foreground: #2d3748;
    --destructive: #e53e3e;
    --border: #cbd5e0;
    --input: #e0e5ec;
    --ring: #4299e1;
    --chart-1: #4299e1;
    --chart-2: #48bb78;
    --chart-3: #ed8936;
    --chart-4: #9f7aea;
    --chart-5: #38b2ac;
    --sidebar: #e0e5ec;
    --sidebar-foreground: #2d3748;
    --sidebar-primary: #4299e1;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #edf2f7;
    --sidebar-accent-foreground: #2d3748;
    --sidebar-border: #cbd5e0;
    --sidebar-ring: #4299e1;
}
.dark {
    --background: #1a202c;
    --foreground: #f7fafc;
    --card: #2d3748;
    --card-foreground: #f7fafc;
    --popover: #2d3748;
    --popover-foreground: #f7fafc;
    --primary: #63b3ed;
    --primary-foreground: #1a202c;
    --secondary: #4a5568;
    --secondary-foreground: #f7fafc;
    --muted: #2d3748;
    --muted-foreground: #a0aec0;
    --accent: #4a5568;
    --accent-foreground: #f7fafc;
    --destructive: #fc8181;
    --border: #4a5568;
    --input: #2d3748;
    --ring: #63b3ed;
    --chart-1: #63b3ed;
    --chart-2: #68d391;
    --chart-3: #fbb6ce;
    --chart-4: #b794f6;
    --chart-5: #4fd1c7;
    --sidebar: #2d3748;
    --sidebar-foreground: #f7fafc;
    --sidebar-primary: #63b3ed;
    --sidebar-primary-foreground: #1a202c;
    --sidebar-accent: #4a5568;
    --sidebar-accent-foreground: #f7fafc;
    --sidebar-border: #4a5568;
    --sidebar-ring: #63b3ed;
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
    }
    button:not([disabled]),
    [role="button"]:not([disabled]) {
        cursor: pointer;
    }
}

/* NEUMORPHISM STYLES */
@layer components {
    .neumorphic-card {
        @apply bg-background;
        box-shadow:
            inset -5px -5px 10px rgba(255, 255, 255, 0.8),
            inset 5px 5px 10px rgba(163, 177, 198, 0.4),
            -5px -5px 10px rgba(255, 255, 255, 0.8),
            5px 5px 10px rgba(163, 177, 198, 0.4);
    }
    .dark .neumorphic-card {
        box-shadow:
            inset -5px -5px 10px rgba(255, 255, 255, 0.05),
            inset 5px 5px 10px rgba(0, 0, 0, 0.5),
            -5px -5px 10px rgba(255, 255, 255, 0.05),
            5px 5px 10px rgba(0, 0, 0, 0.5);
    }
    .neumorphic-button {
        @apply bg-background transition-all duration-200;
        box-shadow:
            -5px -5px 10px rgba(255, 255, 255, 0.8),
            5px 5px 10px rgba(163, 177, 198, 0.4);
    }
    .neumorphic-button:hover {
        box-shadow:
            inset -2px -2px 5px rgba(255, 255, 255, 0.8),
            inset 2px 2px 5px rgba(163, 177, 198, 0.4);
    }
    .neumorphic-button:active {
        box-shadow:
            inset -5px -5px 10px rgba(255, 255, 255, 0.8),
            inset 5px 5px 10px rgba(163, 177, 198, 0.4);
    }
    .dark .neumorphic-button {
        box-shadow:
            -5px -5px 10px rgba(255, 255, 255, 0.05),
            5px 5px 10px rgba(0, 0, 0, 0.5);
    }
    .dark .neumorphic-button:hover {
        box-shadow:
            inset -2px -2px 5px rgba(255, 255, 255, 0.05),
            inset 2px 2px 5px rgba(0, 0, 0, 0.5);
    }
    .dark .neumorphic-button:active {
        box-shadow:
            inset -5px -5px 10px rgba(255, 255, 255, 0.05),
            inset 5px 5px 10px rgba(0, 0, 0, 0.5);
    }
    .neumorphic-button-primary {
        @apply bg-primary text-primary-foreground transition-all duration-200;
        box-shadow:
            -5px -5px 10px rgba(255, 255, 255, 0.8),
            5px 5px 10px rgba(163, 177, 198, 0.4);
    }
    .neumorphic-button-primary:hover {
        @apply bg-primary/90;
        box-shadow:
            inset -2px -2px 5px rgba(255, 255, 255, 0.8),
            inset 2px 2px 5px rgba(163, 177, 198, 0.4);
    }
    .neumorphic-input {
        @apply bg-background;
        box-shadow:
            inset -5px -5px 10px rgba(255, 255, 255, 0.8),
            inset 5px 5px 10px rgba(163, 177, 198, 0.4);
    }
    .dark .neumorphic-input {
        box-shadow:
            inset -5px -5px 10px rgba(255, 255, 255, 0.05),
            inset 5px 5px 10px rgba(0, 0, 0, 0.5);
    }
    .neumorphic-logo {
        @apply bg-background;
        box-shadow:
            -3px -3px 6px rgba(255, 255, 255, 0.8),
            3px 3px 6px rgba(163, 177, 198, 0.4);
    }
    .dark .neumorphic-logo {
        box-shadow:
            -3px -3px 6px rgba(255, 255, 255, 0.05),
            3px 3px 6px rgba(0, 0, 0, 0.5);
    }
    .neumorphic-hero-image {
        @apply bg-background;
        box-shadow:
            -10px -10px 20px rgba(255, 255, 255, 0.8),
            10px 10px 20px rgba(163, 177, 198, 0.4);
    }
    .dark .neumorphic-hero-image {
        box-shadow:
            -10px -10px 20px rgba(255, 255, 255, 0.05),
            10px 10px 20px rgba(0, 0, 0, 0.5);
    }
    .neumorphic-floating-card {
        @apply bg-background/90;
        box-shadow:
            -5px -5px 10px rgba(255, 255, 255, 0.8),
            5px 5px 10px rgba(163, 177, 198, 0.4);
    }
    .dark .neumorphic-floating-card {
        @apply bg-background/90;
        box-shadow:
            -5px -5px 10px rgba(255, 255, 255, 0.05),
            5px 5px 10px rgba(0, 0, 0, 0.5);
    }
    .neumorphic-icon-container {
        @apply bg-background;
        box-shadow:
            inset -3px -3px 6px rgba(255, 255, 255, 0.8),
            inset 3px 3px 6px rgba(163, 177, 198, 0.4);
    }
    .dark .neumorphic-icon-container {
        box-shadow:
            inset -3px -3px 6px rgba(255, 255, 255, 0.05),
            inset 3px 3px 6px rgba(0, 0, 0, 0.5);
    }
    .neumorphic-cta-card {
        @apply bg-background;
        box-shadow:
            inset -10px -10px 20px rgba(255, 255, 255, 0.8),
            inset 10px 10px 20px rgba(163, 177, 198, 0.4),
            -10px -10px 20px rgba(255, 255, 255, 0.8),
            10px 10px 20px rgba(163, 177, 198, 0.4);
    }
    .dark .neumorphic-cta-card {
        box-shadow:
            inset -10px -10px 20px rgba(255, 255, 255, 0.05),
            inset 10px 10px 20px rgba(0, 0, 0, 0.5),
            -10px -10px 20px rgba(255, 255, 255, 0.05),
            10px 10px 20px rgba(0, 0, 0, 0.5);
    }
    .line-clamp-2 {
        display: -webkit-box;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}
