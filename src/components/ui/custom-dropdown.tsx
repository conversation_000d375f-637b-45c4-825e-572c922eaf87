//src/components/ui/custom-dropdown.tsx
"use client";
import { type FC, useState, useEffect, useRef } from "react";
import { AnimatePresence, motion, useReducedMotion } from "framer-motion";
import { cn } from "@/lib/utils";

const springTransition = {
    type: "spring" as const,
    mass: 0.5,
    damping: 11.5,
    stiffness: 100,
    restDelta: 0.001,
    restSpeed: 0.001,
} as const;

const variants = {
    initial: {
        opacity: 0,
        y: -100,
    },
    animate: {
        opacity: 1,
        y: 0,
    },
    exit: {
        opacity: 0,
        y: -100,
    },
    transition: springTransition,
} as const;

interface CustomDropdownProps {
    children: React.ReactNode;
    onClick?: () => void;
    className?: string;
    onKeyDown?: (e: React.KeyboardEvent) => void;
}

const CustomDropdown: FC<CustomDropdownProps> = ({
    children,
    onClick,
    className,
    onKeyDown,
    ...props
}: CustomDropdownProps) => {
    const [isMounted, setIsMounted] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const reduceMotion = useReducedMotion();

    useEffect(() => {
        setIsMounted(true);
    }, []);

    if (!isMounted) {
        return null;
    }

    return (
        <motion.div
            className={cn(
                "absolute -top-3/4 right-[27px]",
                "pt-2 pb-4 px-3",
                "rounded-lg",
                "grid grid-flow-row space-y-3",
                "items-center justify-center",
                "min-w-fit",
                "font-medium tracking-widest",
                "bg-slate-300/60 dark:bg-slate-800/60",
                "will-change-[transform,opacity]",
                "z-0",
                className,
            )}
            variants={variants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={reduceMotion ? { duration: 0 } : springTransition}
            ref={dropdownRef}
            {...props}
            onClick={onClick}
            onKeyDown={onKeyDown}
            role="menu"
            tabIndex={0}
            aria-label="Custom Dropdown"
            aria-orientation="vertical"
            aria-controls="custom-dropdown"
            aria-labelledby="custom-dropdown-button"
            aria-describedby="custom-dropdown-description"
        >
            {children}
        </motion.div>
    );
};

export { CustomDropdown };
